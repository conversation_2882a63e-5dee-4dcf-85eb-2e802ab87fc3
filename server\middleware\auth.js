const jwt = require('jsonwebtoken');
const db = require('../config/database');

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'NO_TOKEN'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await db.get(
      'SELECT id, username, email, full_name, role FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid token - user not found',
        code: 'INVALID_TOKEN'
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    } else {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ 
        error: 'Authentication error',
        code: 'AUTH_ERROR'
      });
    }
  }
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ 
      error: 'Admin access required',
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  }
  next();
};

// Middleware to check resource ownership or admin
const checkOwnership = (resourceType) => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params.id;
      const userId = req.user.id;
      const isAdmin = req.user.role === 'admin';

      // Admin can access everything
      if (isAdmin) {
        return next();
      }

      let query;
      switch (resourceType) {
        case 'prompt':
          query = 'SELECT created_by FROM prompts WHERE id = ?';
          break;
        case 'category':
          query = 'SELECT created_by FROM categories WHERE id = ?';
          break;
        default:
          return res.status(400).json({ 
            error: 'Invalid resource type',
            code: 'INVALID_RESOURCE'
          });
      }

      const resource = await db.get(query, [resourceId]);
      
      if (!resource) {
        return res.status(404).json({ 
          error: `${resourceType} not found`,
          code: 'RESOURCE_NOT_FOUND'
        });
      }

      if (resource.created_by !== userId) {
        return res.status(403).json({ 
          error: `You can only modify your own ${resourceType}s`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      res.status(500).json({ 
        error: 'Permission check failed',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requireAdmin,
  checkOwnership
};
