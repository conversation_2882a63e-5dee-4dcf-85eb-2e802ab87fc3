{"name": "prompt-manager-server", "version": "1.0.0", "description": "Multi-user AI Prompt Manager Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-db.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["ai", "prompts", "management", "collaboration"], "author": "AI Prompt Manager Team", "license": "MIT"}