const express = require('express');
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all categories
router.get('/', authenticateToken, async (req, res) => {
  try {
    const categories = await db.all(`
      SELECT 
        c.*,
        COUNT(p.id) as prompt_count
      FROM categories c
      LEFT JOIN prompts p ON c.id = p.category_id
      GROUP BY c.id
      ORDER BY c.name
    `);

    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      color: category.color,
      promptCount: category.prompt_count,
      createdAt: category.created_at
    }));

    res.json({ categories: formattedCategories });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      error: 'Failed to fetch categories'
    });
  }
});

// Create new category (admin only)
router.post('/', authenticateToken, requireAdmin, [
  body('name')
    .isLength({ min: 1, max: 50 })
    .withMessage('Name is required and must be less than 50 characters'),
  body('description')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Description must be less than 200 characters'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { name, description, color = '#3B82F6' } = req.body;
    const userId = req.user.id;

    // Check if category already exists
    const existingCategory = await db.get(
      'SELECT id FROM categories WHERE name = ?',
      [name]
    );

    if (existingCategory) {
      return res.status(409).json({
        error: 'Category already exists'
      });
    }

    // Create category
    const result = await db.run(
      'INSERT INTO categories (name, description, color, created_by) VALUES (?, ?, ?, ?)',
      [name, description, color, userId]
    );

    const category = await db.get(
      'SELECT * FROM categories WHERE id = ?',
      [result.id]
    );

    res.status(201).json({
      message: 'Category created successfully',
      category: {
        id: category.id,
        name: category.name,
        description: category.description,
        color: category.color,
        promptCount: 0,
        createdAt: category.created_at
      }
    });

  } catch (error) {
    console.error('Create category error:', error);
    res.status(500).json({
      error: 'Failed to create category'
    });
  }
});

// Update category (admin only)
router.put('/:id', authenticateToken, requireAdmin, [
  body('name')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Name must be less than 50 characters'),
  body('description')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Description must be less than 200 characters'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const categoryId = req.params.id;
    const { name, description, color } = req.body;

    // Check if category exists
    const existingCategory = await db.get(
      'SELECT * FROM categories WHERE id = ?',
      [categoryId]
    );

    if (!existingCategory) {
      return res.status(404).json({
        error: 'Category not found'
      });
    }

    // Check if name is already taken by another category
    if (name) {
      const nameConflict = await db.get(
        'SELECT id FROM categories WHERE name = ? AND id != ?',
        [name, categoryId]
      );

      if (nameConflict) {
        return res.status(409).json({
          error: 'Category name already exists'
        });
      }
    }

    // Prepare update fields
    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    if (color !== undefined) {
      updateFields.push('color = ?');
      updateValues.push(color);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        error: 'No fields to update'
      });
    }

    updateValues.push(categoryId);

    // Update category
    await db.run(
      `UPDATE categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // Get updated category with prompt count
    const updatedCategory = await db.get(`
      SELECT 
        c.*,
        COUNT(p.id) as prompt_count
      FROM categories c
      LEFT JOIN prompts p ON c.id = p.category_id
      WHERE c.id = ?
      GROUP BY c.id
    `, [categoryId]);

    res.json({
      message: 'Category updated successfully',
      category: {
        id: updatedCategory.id,
        name: updatedCategory.name,
        description: updatedCategory.description,
        color: updatedCategory.color,
        promptCount: updatedCategory.prompt_count,
        createdAt: updatedCategory.created_at
      }
    });

  } catch (error) {
    console.error('Update category error:', error);
    res.status(500).json({
      error: 'Failed to update category'
    });
  }
});

// Delete category (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const categoryId = req.params.id;

    // Check if category exists
    const category = await db.get(
      'SELECT * FROM categories WHERE id = ?',
      [categoryId]
    );

    if (!category) {
      return res.status(404).json({
        error: 'Category not found'
      });
    }

    // Check if category has prompts
    const promptCount = await db.get(
      'SELECT COUNT(*) as count FROM prompts WHERE category_id = ?',
      [categoryId]
    );

    if (promptCount.count > 0) {
      return res.status(400).json({
        error: 'Cannot delete category with existing prompts',
        promptCount: promptCount.count
      });
    }

    // Delete category
    await db.run(
      'DELETE FROM categories WHERE id = ?',
      [categoryId]
    );

    res.json({
      message: 'Category deleted successfully'
    });

  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({
      error: 'Failed to delete category'
    });
  }
});

module.exports = router;
