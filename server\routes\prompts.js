const express = require('express');
const { body, validationResult, query } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Get all prompts (with filtering and pagination)
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('category').optional().isString().withMessage('Category must be a string'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('tags').optional().isString().withMessage('Tags must be a string'),
  query('own_only').optional().isBoolean().withMessage('Own_only must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      page = 1,
      limit = 50,
      category,
      search,
      tags,
      own_only = false
    } = req.query;

    const offset = (page - 1) * limit;
    const userId = req.user.id;

    // Build query
    let whereConditions = ['(p.is_public = 1 OR p.created_by = ?)'];
    let queryParams = [userId];

    // Filter by ownership
    if (own_only === 'true') {
      whereConditions = ['p.created_by = ?'];
      queryParams = [userId];
    }

    // Filter by category
    if (category) {
      whereConditions.push('c.name = ?');
      queryParams.push(category);
    }

    // Filter by search term
    if (search) {
      whereConditions.push('(p.title LIKE ? OR p.content LIKE ? OR p.description LIKE ?)');
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // Filter by tags
    if (tags) {
      const tagList = tags.split(',').map(tag => tag.trim());
      const tagPlaceholders = tagList.map(() => '?').join(',');
      whereConditions.push(`p.id IN (
        SELECT pt.prompt_id
        FROM prompt_tags pt
        JOIN tags t ON pt.tag_id = t.id
        WHERE t.name IN (${tagPlaceholders})
        GROUP BY pt.prompt_id
        HAVING COUNT(DISTINCT t.name) = ?
      )`);
      queryParams.push(...tagList, tagList.length);
    }

    const whereClause = whereConditions.join(' AND ');

    // Get prompts with user and category info
    const prompts = await db.all(`
      SELECT
        p.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        c.name as category_name,
        c.color as category_color,
        GROUP_CONCAT(t.name) as tag_names
      FROM prompts p
      LEFT JOIN users u ON p.created_by = u.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE ${whereClause}
      GROUP BY p.id
      ORDER BY p.updated_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // Get total count
    const countResult = await db.get(`
      SELECT COUNT(DISTINCT p.id) as total
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE ${whereClause}
    `, queryParams);

    // Format prompts
    const formattedPrompts = prompts.map(prompt => ({
      id: prompt.id,
      title: prompt.title,
      content: prompt.content,
      description: prompt.description,
      category: prompt.category_name,
      categoryColor: prompt.category_color,
      tags: prompt.tag_names ? prompt.tag_names.split(',') : [],
      isFavorite: Boolean(prompt.is_favorite),
      isPublic: Boolean(prompt.is_public),
      version: prompt.version,
      usageCount: prompt.usage_count,
      createdAt: prompt.created_at,
      updatedAt: prompt.updated_at,
      lastUsed: prompt.last_used,
      createdBy: {
        id: prompt.created_by,
        username: prompt.created_by_username,
        fullName: prompt.created_by_name
      },
      canEdit: prompt.created_by === userId || req.user.role === 'admin'
    }));

    res.json({
      prompts: formattedPrompts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    });

  } catch (error) {
    console.error('Get prompts error:', error);
    res.status(500).json({
      error: 'Failed to fetch prompts'
    });
  }
});

// Get single prompt
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const promptId = req.params.id;
    const userId = req.user.id;

    const prompt = await db.get(`
      SELECT
        p.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        c.name as category_name,
        c.color as category_color,
        GROUP_CONCAT(t.name) as tag_names
      FROM prompts p
      LEFT JOIN users u ON p.created_by = u.id
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.id = ? AND (p.is_public = 1 OR p.created_by = ?)
      GROUP BY p.id
    `, [promptId, userId]);

    if (!prompt) {
      return res.status(404).json({
        error: 'Prompt not found'
      });
    }

    const formattedPrompt = {
      id: prompt.id,
      title: prompt.title,
      content: prompt.content,
      description: prompt.description,
      category: prompt.category_name,
      categoryColor: prompt.category_color,
      tags: prompt.tag_names ? prompt.tag_names.split(',') : [],
      isFavorite: Boolean(prompt.is_favorite),
      isPublic: Boolean(prompt.is_public),
      version: prompt.version,
      usageCount: prompt.usage_count,
      createdAt: prompt.created_at,
      updatedAt: prompt.updated_at,
      lastUsed: prompt.last_used,
      createdBy: {
        id: prompt.created_by,
        username: prompt.created_by_username,
        fullName: prompt.created_by_name
      },
      canEdit: prompt.created_by === userId || req.user.role === 'admin'
    };

    res.json({ prompt: formattedPrompt });

  } catch (error) {
    console.error('Get prompt error:', error);
    res.status(500).json({
      error: 'Failed to fetch prompt'
    });
  }
});

// Create new prompt
router.post('/', authenticateToken, [
  body('title')
    .isLength({ min: 1, max: 200 })
    .withMessage('Title is required and must be less than 200 characters'),
  body('content')
    .isLength({ min: 1, max: 10000 })
    .withMessage('Content is required and must be less than 10000 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('category')
    .notEmpty()
    .withMessage('Category is required'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('isFavorite')
    .optional()
    .isBoolean()
    .withMessage('isFavorite must be a boolean'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      title,
      content,
      description,
      category,
      tags = [],
      isFavorite = false,
      isPublic = true
    } = req.body;

    const userId = req.user.id;

    // Get category ID
    const categoryRecord = await db.get(
      'SELECT id FROM categories WHERE name = ?',
      [category]
    );

    if (!categoryRecord) {
      return res.status(400).json({
        error: 'Invalid category'
      });
    }

    // Start transaction
    await db.beginTransaction();

    try {
      // Create prompt
      const promptResult = await db.run(`
        INSERT INTO prompts (
          title, content, description, category_id, created_by,
          is_favorite, is_public, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [title, content, description, categoryRecord.id, userId, isFavorite ? 1 : 0, isPublic ? 1 : 0]);

      const promptId = promptResult.id;

      // Handle tags
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          if (tagName && tagName.trim()) {
            // Insert tag if it doesn't exist
            await db.run(
              'INSERT OR IGNORE INTO tags (name) VALUES (?)',
              [tagName.trim()]
            );

            // Get tag ID
            const tag = await db.get(
              'SELECT id FROM tags WHERE name = ?',
              [tagName.trim()]
            );

            // Link prompt to tag
            await db.run(
              'INSERT INTO prompt_tags (prompt_id, tag_id) VALUES (?, ?)',
              [promptId, tag.id]
            );
          }
        }
      }

      await db.commit();

      // Get the created prompt with all details
      const createdPrompt = await db.get(`
        SELECT
          p.*,
          u.username as created_by_username,
          u.full_name as created_by_name,
          c.name as category_name,
          c.color as category_color,
          GROUP_CONCAT(t.name) as tag_names
        FROM prompts p
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
        LEFT JOIN tags t ON pt.tag_id = t.id
        WHERE p.id = ?
        GROUP BY p.id
      `, [promptId]);

      const formattedPrompt = {
        id: createdPrompt.id,
        title: createdPrompt.title,
        content: createdPrompt.content,
        description: createdPrompt.description,
        category: createdPrompt.category_name,
        categoryColor: createdPrompt.category_color,
        tags: createdPrompt.tag_names ? createdPrompt.tag_names.split(',') : [],
        isFavorite: Boolean(createdPrompt.is_favorite),
        isPublic: Boolean(createdPrompt.is_public),
        version: createdPrompt.version,
        usageCount: createdPrompt.usage_count,
        createdAt: createdPrompt.created_at,
        updatedAt: createdPrompt.updated_at,
        lastUsed: createdPrompt.last_used,
        createdBy: {
          id: createdPrompt.created_by,
          username: createdPrompt.created_by_username,
          fullName: createdPrompt.created_by_name
        },
        canEdit: true
      };

      res.status(201).json({
        message: 'Prompt created successfully',
        prompt: formattedPrompt
      });

    } catch (error) {
      await db.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Create prompt error:', error);
    res.status(500).json({
      error: 'Failed to create prompt'
    });
  }
});

// Update prompt
router.put('/:id', authenticateToken, checkOwnership('prompt'), [
  body('title')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be less than 200 characters'),
  body('content')
    .optional()
    .isLength({ min: 1, max: 10000 })
    .withMessage('Content must be less than 10000 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('category')
    .optional()
    .notEmpty()
    .withMessage('Category cannot be empty'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('isFavorite')
    .optional()
    .isBoolean()
    .withMessage('isFavorite must be a boolean'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const promptId = req.params.id;
    const {
      title,
      content,
      description,
      category,
      tags,
      isFavorite,
      isPublic
    } = req.body;

    // Get current prompt
    const currentPrompt = await db.get(
      'SELECT * FROM prompts WHERE id = ?',
      [promptId]
    );

    if (!currentPrompt) {
      return res.status(404).json({
        error: 'Prompt not found'
      });
    }

    // Start transaction
    await db.beginTransaction();

    try {
      // Prepare update fields
      const updateFields = [];
      const updateValues = [];

      if (title !== undefined) {
        updateFields.push('title = ?');
        updateValues.push(title);
      }
      if (content !== undefined) {
        updateFields.push('content = ?');
        updateValues.push(content);
      }
      if (description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(description);
      }
      if (isFavorite !== undefined) {
        updateFields.push('is_favorite = ?');
        updateValues.push(isFavorite ? 1 : 0);
      }
      if (isPublic !== undefined) {
        updateFields.push('is_public = ?');
        updateValues.push(isPublic ? 1 : 0);
      }

      // Handle category
      if (category !== undefined) {
        const categoryRecord = await db.get(
          'SELECT id FROM categories WHERE name = ?',
          [category]
        );

        if (!categoryRecord) {
          await db.rollback();
          return res.status(400).json({
            error: 'Invalid category'
          });
        }

        updateFields.push('category_id = ?');
        updateValues.push(categoryRecord.id);
      }

      // Update version and timestamp
      updateFields.push('version = version + 1');
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      // Update prompt
      if (updateFields.length > 0) {
        updateValues.push(promptId);
        await db.run(
          `UPDATE prompts SET ${updateFields.join(', ')} WHERE id = ?`,
          updateValues
        );
      }

      // Handle tags update
      if (tags !== undefined) {
        // Remove existing tags
        await db.run(
          'DELETE FROM prompt_tags WHERE prompt_id = ?',
          [promptId]
        );

        // Add new tags
        if (tags.length > 0) {
          for (const tagName of tags) {
            if (tagName && tagName.trim()) {
              // Insert tag if it doesn't exist
              await db.run(
                'INSERT OR IGNORE INTO tags (name) VALUES (?)',
                [tagName.trim()]
              );

              // Get tag ID
              const tag = await db.get(
                'SELECT id FROM tags WHERE name = ?',
                [tagName.trim()]
              );

              // Link prompt to tag
              await db.run(
                'INSERT INTO prompt_tags (prompt_id, tag_id) VALUES (?, ?)',
                [promptId, tag.id]
              );
            }
          }
        }
      }

      await db.commit();

      // Get updated prompt
      const updatedPrompt = await db.get(`
        SELECT
          p.*,
          u.username as created_by_username,
          u.full_name as created_by_name,
          c.name as category_name,
          c.color as category_color,
          GROUP_CONCAT(t.name) as tag_names
        FROM prompts p
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
        LEFT JOIN tags t ON pt.tag_id = t.id
        WHERE p.id = ?
        GROUP BY p.id
      `, [promptId]);

      const formattedPrompt = {
        id: updatedPrompt.id,
        title: updatedPrompt.title,
        content: updatedPrompt.content,
        description: updatedPrompt.description,
        category: updatedPrompt.category_name,
        categoryColor: updatedPrompt.category_color,
        tags: updatedPrompt.tag_names ? updatedPrompt.tag_names.split(',') : [],
        isFavorite: Boolean(updatedPrompt.is_favorite),
        isPublic: Boolean(updatedPrompt.is_public),
        version: updatedPrompt.version,
        usageCount: updatedPrompt.usage_count,
        createdAt: updatedPrompt.created_at,
        updatedAt: updatedPrompt.updated_at,
        lastUsed: updatedPrompt.last_used,
        createdBy: {
          id: updatedPrompt.created_by,
          username: updatedPrompt.created_by_username,
          fullName: updatedPrompt.created_by_name
        },
        canEdit: true
      };

      res.json({
        message: 'Prompt updated successfully',
        prompt: formattedPrompt
      });

    } catch (error) {
      await db.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Update prompt error:', error);
    res.status(500).json({
      error: 'Failed to update prompt'
    });
  }
});

// Delete prompt
router.delete('/:id', authenticateToken, checkOwnership('prompt'), async (req, res) => {
  try {
    const promptId = req.params.id;

    // Start transaction
    await db.beginTransaction();

    try {
      // Delete prompt tags
      await db.run(
        'DELETE FROM prompt_tags WHERE prompt_id = ?',
        [promptId]
      );

      // Delete prompt
      const result = await db.run(
        'DELETE FROM prompts WHERE id = ?',
        [promptId]
      );

      if (result.changes === 0) {
        await db.rollback();
        return res.status(404).json({
          error: 'Prompt not found'
        });
      }

      await db.commit();

      res.json({
        message: 'Prompt deleted successfully'
      });

    } catch (error) {
      await db.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Delete prompt error:', error);
    res.status(500).json({
      error: 'Failed to delete prompt'
    });
  }
});

// Increment usage count
router.post('/:id/use', authenticateToken, async (req, res) => {
  try {
    const promptId = req.params.id;
    const userId = req.user.id;

    // Check if prompt exists and is accessible
    const prompt = await db.get(
      'SELECT id FROM prompts WHERE id = ? AND (is_public = 1 OR created_by = ?)',
      [promptId, userId]
    );

    if (!prompt) {
      return res.status(404).json({
        error: 'Prompt not found'
      });
    }

    // Increment usage count
    await db.run(
      'UPDATE prompts SET usage_count = usage_count + 1, last_used = CURRENT_TIMESTAMP WHERE id = ?',
      [promptId]
    );

    res.json({
      message: 'Usage count updated'
    });

  } catch (error) {
    console.error('Update usage error:', error);
    res.status(500).json({
      error: 'Failed to update usage count'
    });
  }
});

module.exports = router;
