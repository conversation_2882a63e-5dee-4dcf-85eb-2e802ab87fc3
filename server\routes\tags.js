const express = require('express');
const db = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all tags with usage count
router.get('/', authenticateToken, async (req, res) => {
  try {
    const tags = await db.all(`
      SELECT 
        t.*,
        COUNT(pt.prompt_id) as prompt_count
      FROM tags t
      LEFT JOIN prompt_tags pt ON t.id = pt.tag_id
      LEFT JOIN prompts p ON pt.prompt_id = p.id AND (p.is_public = 1 OR p.created_by = ?)
      GROUP BY t.id
      HAVING prompt_count > 0
      ORDER BY prompt_count DESC, t.name
    `, [req.user.id]);

    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      promptCount: tag.prompt_count,
      createdAt: tag.created_at
    }));

    res.json({ tags: formattedTags });

  } catch (error) {
    console.error('Get tags error:', error);
    res.status(500).json({
      error: 'Failed to fetch tags'
    });
  }
});

// Get popular tags (most used)
router.get('/popular', authenticateToken, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    const tags = await db.all(`
      SELECT 
        t.*,
        COUNT(pt.prompt_id) as prompt_count
      FROM tags t
      LEFT JOIN prompt_tags pt ON t.id = pt.tag_id
      LEFT JOIN prompts p ON pt.prompt_id = p.id AND (p.is_public = 1 OR p.created_by = ?)
      GROUP BY t.id
      HAVING prompt_count > 0
      ORDER BY prompt_count DESC, t.name
      LIMIT ?
    `, [req.user.id, limit]);

    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      promptCount: tag.prompt_count,
      createdAt: tag.created_at
    }));

    res.json({ tags: formattedTags });

  } catch (error) {
    console.error('Get popular tags error:', error);
    res.status(500).json({
      error: 'Failed to fetch popular tags'
    });
  }
});

// Search tags
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { q: query } = req.query;
    
    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        error: 'Search query must be at least 2 characters'
      });
    }

    const tags = await db.all(`
      SELECT 
        t.*,
        COUNT(pt.prompt_id) as prompt_count
      FROM tags t
      LEFT JOIN prompt_tags pt ON t.id = pt.tag_id
      LEFT JOIN prompts p ON pt.prompt_id = p.id AND (p.is_public = 1 OR p.created_by = ?)
      WHERE t.name LIKE ?
      GROUP BY t.id
      HAVING prompt_count > 0
      ORDER BY prompt_count DESC, t.name
      LIMIT 50
    `, [req.user.id, `%${query.trim()}%`]);

    const formattedTags = tags.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      promptCount: tag.prompt_count,
      createdAt: tag.created_at
    }));

    res.json({ tags: formattedTags });

  } catch (error) {
    console.error('Search tags error:', error);
    res.status(500).json({
      error: 'Failed to search tags'
    });
  }
});

// Get tag statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await db.get(`
      SELECT 
        COUNT(DISTINCT t.id) as total_tags,
        COUNT(DISTINCT pt.prompt_id) as tagged_prompts,
        AVG(tag_counts.prompt_count) as avg_prompts_per_tag
      FROM tags t
      LEFT JOIN prompt_tags pt ON t.id = pt.tag_id
      LEFT JOIN prompts p ON pt.prompt_id = p.id AND (p.is_public = 1 OR p.created_by = ?)
      LEFT JOIN (
        SELECT 
          t2.id,
          COUNT(pt2.prompt_id) as prompt_count
        FROM tags t2
        LEFT JOIN prompt_tags pt2 ON t2.id = pt2.tag_id
        LEFT JOIN prompts p2 ON pt2.prompt_id = p2.id AND (p2.is_public = 1 OR p2.created_by = ?)
        GROUP BY t2.id
      ) tag_counts ON t.id = tag_counts.id
    `, [req.user.id, req.user.id]);

    // Get most popular tag
    const mostPopularTag = await db.get(`
      SELECT 
        t.name,
        COUNT(pt.prompt_id) as prompt_count
      FROM tags t
      LEFT JOIN prompt_tags pt ON t.id = pt.tag_id
      LEFT JOIN prompts p ON pt.prompt_id = p.id AND (p.is_public = 1 OR p.created_by = ?)
      GROUP BY t.id
      ORDER BY prompt_count DESC
      LIMIT 1
    `, [req.user.id]);

    res.json({
      stats: {
        totalTags: stats.total_tags || 0,
        taggedPrompts: stats.tagged_prompts || 0,
        averagePromptsPerTag: Math.round((stats.avg_prompts_per_tag || 0) * 100) / 100,
        mostPopularTag: mostPopularTag ? {
          name: mostPopularTag.name,
          promptCount: mostPopularTag.prompt_count
        } : null
      }
    });

  } catch (error) {
    console.error('Get tag stats error:', error);
    res.status(500).json({
      error: 'Failed to fetch tag statistics'
    });
  }
});

module.exports = router;
