const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Ensure database directory exists
const dbDir = path.join(__dirname, '..', 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const dbPath = path.join(dbDir, 'prompts.db');

// Create database connection
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Create tables
const createTables = () => {
  // Users table
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      full_name TEXT,
      role TEXT DEFAULT 'user',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating users table:', err.message);
    } else {
      console.log('✅ Users table created/verified');
    }
  });

  // Categories table
  db.run(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      color TEXT DEFAULT '#3B82F6',
      created_by INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users (id)
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating categories table:', err.message);
    } else {
      console.log('✅ Categories table created/verified');
    }
  });

  // Prompts table
  db.run(`
    CREATE TABLE IF NOT EXISTS prompts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      description TEXT,
      category_id INTEGER,
      created_by INTEGER NOT NULL,
      is_favorite BOOLEAN DEFAULT 0,
      is_public BOOLEAN DEFAULT 1,
      version INTEGER DEFAULT 1,
      usage_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_used DATETIME,
      FOREIGN KEY (category_id) REFERENCES categories (id),
      FOREIGN KEY (created_by) REFERENCES users (id)
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating prompts table:', err.message);
    } else {
      console.log('✅ Prompts table created/verified');
    }
  });

  // Tags table
  db.run(`
    CREATE TABLE IF NOT EXISTS tags (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      color TEXT DEFAULT '#10B981',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating tags table:', err.message);
    } else {
      console.log('✅ Tags table created/verified');
    }
  });

  // Prompt tags junction table
  db.run(`
    CREATE TABLE IF NOT EXISTS prompt_tags (
      prompt_id INTEGER,
      tag_id INTEGER,
      PRIMARY KEY (prompt_id, tag_id),
      FOREIGN KEY (prompt_id) REFERENCES prompts (id) ON DELETE CASCADE,
      FOREIGN KEY (tag_id) REFERENCES tags (id) ON DELETE CASCADE
    )
  `, (err) => {
    if (err) {
      console.error('❌ Error creating prompt_tags table:', err.message);
    } else {
      console.log('✅ Prompt_tags table created/verified');
    }
  });
};

// Insert default data
const insertDefaultData = () => {
  // Insert default categories
  const defaultCategories = [
    { name: 'Content Creation', description: 'Prompts for creating various types of content', color: '#3B82F6' },
    { name: 'Code Generation', description: 'Prompts for generating and reviewing code', color: '#10B981' },
    { name: 'Analysis', description: 'Prompts for data analysis and insights', color: '#F59E0B' },
    { name: 'Creative Writing', description: 'Prompts for creative and artistic writing', color: '#8B5CF6' },
    { name: 'Business', description: 'Prompts for business and professional use', color: '#EF4444' },
    { name: 'Research', description: 'Prompts for research and investigation', color: '#06B6D4' }
  ];

  defaultCategories.forEach(category => {
    db.run(
      'INSERT OR IGNORE INTO categories (name, description, color) VALUES (?, ?, ?)',
      [category.name, category.description, category.color],
      (err) => {
        if (err) {
          console.error('❌ Error inserting category:', err.message);
        }
      }
    );
  });

  console.log('✅ Default categories inserted');
};

// Execute initialization
createTables();

// Wait a bit for tables to be created, then insert default data
setTimeout(() => {
  insertDefaultData();
  
  setTimeout(() => {
    db.close((err) => {
      if (err) {
        console.error('❌ Error closing database:', err.message);
      } else {
        console.log('✅ Database initialization completed successfully');
      }
    });
  }, 1000);
}, 1000);
