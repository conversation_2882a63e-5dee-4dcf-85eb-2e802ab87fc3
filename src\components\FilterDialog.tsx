
import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Category, Tag } from '@/types/prompt';

interface FilterDialogProps {
  categories: Category[];
  tags: Tag[];
  selectedCategory: string;
  selectedTags: string[];
  onCategorySelect: (category: string) => void;
  onTagSelect: (tags: string[]) => void;
  onClose: () => void;
}

export const FilterDialog: React.FC<FilterDialogProps> = ({
  categories,
  tags,
  selectedCategory,
  selectedTags,
  onCategorySelect,
  onTagSelect,
  onClose
}) => {
  const handleTagToggle = (tagName: string) => {
    if (selectedTags.includes(tagName)) {
      onTagSelect(selectedTags.filter(t => t !== tagName));
    } else {
      onTagSelect([...selectedTags, tagName]);
    }
  };

  const clearAllFilters = () => {
    onCategorySelect('all');
    onTagSelect([]);
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Filter Prompts</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Categories */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Categories</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <button
                onClick={() => onCategorySelect('all')}
                className={`p-3 text-left rounded-lg border transition-colors ${
                  selectedCategory === 'all'
                    ? 'bg-blue-50 border-blue-200 text-blue-900'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="font-medium">All Categories</div>
                <div className="text-sm text-gray-500">
                  {categories.reduce((sum, cat) => sum + cat.promptCount, 0)} prompts
                </div>
              </button>
              
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => onCategorySelect(category.name)}
                  className={`p-3 text-left rounded-lg border transition-colors ${
                    selectedCategory === category.name
                      ? 'bg-blue-50 border-blue-200 text-blue-900'
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {category.promptCount} prompts
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Tags */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Tags</h3>
            <div className="max-h-60 overflow-y-auto">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {tags.map((tag) => (
                  <button
                    key={tag.id}
                    onClick={() => handleTagToggle(tag.name)}
                    className={`p-2 text-left rounded-lg border transition-colors ${
                      selectedTags.includes(tag.name)
                        ? 'bg-green-50 border-green-200 text-green-900'
                        : 'bg-white border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">#{tag.name}</div>
                    <div className="text-sm text-gray-500">
                      {tag.promptCount} prompts
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Selected Filters Summary */}
          {(selectedCategory !== 'all' || selectedTags.length > 0) && (
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">Active Filters</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFilters}
                >
                  Clear All
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedCategory !== 'all' && (
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2">
                    Category: {selectedCategory}
                    <button
                      onClick={() => onCategorySelect('all')}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                )}
                {selectedTags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center gap-2"
                  >
                    #{tag}
                    <button
                      onClick={() => handleTagToggle(tag)}
                      className="text-green-600 hover:text-green-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
