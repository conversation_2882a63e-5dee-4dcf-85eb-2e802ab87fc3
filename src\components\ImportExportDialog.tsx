
import React, { useState } from 'react';
import { Download, Upload, FileText, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Prompt } from '@/types/prompt';

interface ImportExportDialogProps {
  prompts: Prompt[];
  onClose: () => void;
}

export const ImportExportDialog: React.FC<ImportExportDialogProps> = ({
  prompts,
  onClose
}) => {
  const [importData, setImportData] = useState('');
  const [importError, setImportError] = useState('');

  const handleExport = (format: 'json' | 'csv') => {
    let content = '';
    let filename = '';
    let mimeType = '';

    if (format === 'json') {
      content = JSON.stringify({
        prompts,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      }, null, 2);
      filename = `ai-prompts-${new Date().toISOString().split('T')[0]}.json`;
      mimeType = 'application/json';
    } else {
      // CSV format
      const headers = ['Title', 'Description', 'Category', 'Tags', 'Content', 'Created', 'Updated'];
      const rows = prompts.map(prompt => [
        prompt.title,
        prompt.description || '',
        prompt.category,
        prompt.tags.join(';'),
        prompt.content.replace(/"/g, '""'), // Escape quotes for CSV
        prompt.createdAt.toISOString(),
        prompt.updatedAt.toISOString()
      ]);

      content = [headers, ...rows]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');
      
      filename = `ai-prompts-${new Date().toISOString().split('T')[0]}.csv`;
      mimeType = 'text/csv';
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    try {
      setImportError('');
      const data = JSON.parse(importData);
      
      if (!data.prompts || !Array.isArray(data.prompts)) {
        throw new Error('Invalid format: prompts array not found');
      }

      // Validate prompt structure
      const invalidPrompts = data.prompts.filter((prompt: any) => 
        !prompt.title || !prompt.content || !prompt.category
      );

      if (invalidPrompts.length > 0) {
        throw new Error(`Invalid prompts found: missing required fields (title, content, category)`);
      }

      // Import logic would go here - for now just close
      alert(`Successfully imported ${data.prompts.length} prompts!`);
      onClose();
    } catch (error) {
      setImportError(error instanceof Error ? error.message : 'Invalid JSON format');
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Import & Export Prompts</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="export" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="import">Import</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4">
            <div className="text-sm text-gray-600">
              Export your prompts to backup or share with others. Choose your preferred format below.
            </div>
            
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Current Library Stats</h4>
                <div className="text-sm text-blue-700">
                  <p>Total Prompts: {prompts.length}</p>
                  <p>Categories: {[...new Set(prompts.map(p => p.category))].length}</p>
                  <p>Unique Tags: {[...new Set(prompts.flatMap(p => p.tags))].length}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <h4 className="font-medium">JSON Format</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Complete data export including metadata, versions, and statistics.
                  </p>
                  <Button
                    onClick={() => handleExport('json')}
                    className="w-full"
                    variant="outline"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export as JSON
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="w-5 h-5 text-green-600" />
                    <h4 className="font-medium">CSV Format</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Spreadsheet-compatible format for analysis and basic backup.
                  </p>
                  <Button
                    onClick={() => handleExport('csv')}
                    className="w-full"
                    variant="outline"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export as CSV
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="import" className="space-y-4">
            <div className="text-sm text-gray-600">
              Import prompts from a JSON file. Make sure the file follows the correct format.
            </div>

            {importError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-red-600 mt-0.5" />
                <div className="text-sm text-red-700">{importError}</div>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  JSON Data
                </label>
                <Textarea
                  value={importData}
                  onChange={(e) => setImportData(e.target.value)}
                  placeholder="Paste your JSON data here..."
                  rows={10}
                  className="font-mono text-sm"
                />
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-700">
                    <strong>Important:</strong> Importing will add to your existing prompts. 
                    Make sure to backup your current data before importing.
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={handleImport}
                  disabled={!importData.trim()}
                  className="flex-1"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Import Prompts
                </Button>
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
