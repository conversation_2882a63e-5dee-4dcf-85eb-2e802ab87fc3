
import React from 'react';
import { <PERSON>, Edit3, Trash2, <PERSON><PERSON>, <PERSON>, Hash, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Prompt } from '@/types/prompt';

interface PromptCardProps {
  prompt: Prompt;
  onEdit: (prompt: Prompt) => void;
  onDelete: (id: string) => void;
}

export const PromptCard: React.FC<PromptCardProps> = ({
  prompt,
  onEdit,
  onDelete
}) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy prompt:', error);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Card className="p-6 hover:shadow-lg transition-shadow border-l-4 border-l-blue-500">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold text-gray-900 text-lg">{prompt.title}</h3>
            {prompt.isFavorite && (
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
            )}
          </div>
          {prompt.description && (
            <p className="text-sm text-gray-600 mb-3">{prompt.description}</p>
          )}
        </div>
      </div>

      {/* Prompt Content Preview */}
      <div className="mb-4">
        <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-700 font-mono">
          {prompt.content.length > 150
            ? `${prompt.content.substring(0, 150)}...`
            : prompt.content}
        </div>
      </div>

      {/* Category and Tags */}
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded-full">
            {prompt.category}
          </span>
        </div>
        {prompt.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {prompt.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="bg-gray-100 text-gray-700 px-2 py-1 text-xs rounded-full flex items-center gap-1"
              >
                <Hash className="w-3 h-3" />
                {tag}
              </span>
            ))}
            {prompt.tags.length > 3 && (
              <span className="text-xs text-gray-500 px-2 py-1">
                +{prompt.tags.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="flex items-center gap-4 text-xs text-gray-500 mb-4">
        <div className="flex items-center gap-1">
          <TrendingUp className="w-3 h-3" />
          Used {prompt.usageCount || 0} times
        </div>
        <div className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          Updated {formatDate(prompt.updatedAt)}
        </div>
        <div className="text-gray-400">
          v{prompt.version}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCopy}
          className="flex-1 flex items-center gap-2"
        >
          <Copy className="w-4 h-4" />
          Copy
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(prompt)}
          className="flex items-center gap-2"
        >
          <Edit3 className="w-4 h-4" />
          Edit
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDelete(prompt.id)}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </Card>
  );
};
