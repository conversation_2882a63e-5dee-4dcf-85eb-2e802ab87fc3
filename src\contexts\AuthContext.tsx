import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient, User } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (userData: {
    username: string;
    email: string;
    password: string;
    fullName?: string;
  }) => Promise<void>;
  logout: () => void;
  error: string | null;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user;

  // Clear error
  const clearError = () => setError(null);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      const token = apiClient.getToken();
      
      if (!token) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('🔐 Verifying stored token...');
        const response = await apiClient.verifyToken();
        
        if (response.valid && response.user) {
          setUser(response.user);
          console.log('✅ Token verified, user authenticated:', response.user.username);
        } else {
          console.log('❌ Token invalid, clearing auth');
          apiClient.setToken(null);
        }
      } catch (error) {
        console.error('❌ Token verification failed:', error);
        apiClient.setToken(null);
        setError('Session expired. Please log in again.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔐 Attempting login for:', username);
      const response = await apiClient.login(username, password);
      
      setUser(response.user);
      console.log('✅ Login successful:', response.user.username);
    } catch (error) {
      console.error('❌ Login failed:', error);
      setError(error instanceof Error ? error.message : 'Login failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: {
    username: string;
    email: string;
    password: string;
    fullName?: string;
  }) => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('📝 Attempting registration for:', userData.username);
      const response = await apiClient.register(userData);
      
      setUser(response.user);
      console.log('✅ Registration successful:', response.user.username);
    } catch (error) {
      console.error('❌ Registration failed:', error);
      setError(error instanceof Error ? error.message : 'Registration failed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    console.log('🚪 Logging out user:', user?.username);
    apiClient.logout();
    setUser(null);
    setError(null);
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    error,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
