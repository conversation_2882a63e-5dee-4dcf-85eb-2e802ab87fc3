
import { useState, useEffect, useCallback } from 'react';
import { Prompt, Category, Tag } from '@/types/prompt';
import { apiClient } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';

// Transform API data to match frontend types
const transformApiPrompt = (apiPrompt: any): Prompt => ({
  id: apiPrompt.id.toString(),
  title: apiPrompt.title,
  content: apiPrompt.content,
  description: apiPrompt.description || '',
  category: apiPrompt.category || 'Uncategorized',
  tags: apiPrompt.tags || [],
  isFavorite: apiPrompt.isFavorite || false,
  version: apiPrompt.version || 1,
  createdAt: new Date(apiPrompt.createdAt),
  updatedAt: new Date(apiPrompt.updatedAt),
  lastUsed: apiPrompt.lastUsed ? new Date(apiPrompt.lastUsed) : undefined,
  usageCount: apiPrompt.usageCount || 0,
  // Add metadata for multi-user features
  createdBy: apiPrompt.createdBy,
  canEdit: apiPrompt.canEdit || false,
  isPublic: apiPrompt.isPublic !== false
});

const transformApiCategory = (apiCategory: any): Category => ({
  id: apiCategory.id.toString(),
  name: apiCategory.name,
  description: apiCategory.description || '',
  color: apiCategory.color || '#3B82F6',
  promptCount: apiCategory.promptCount || 0
});

const transformApiTag = (apiTag: any): Tag => ({
  id: apiTag.id.toString(),
  name: apiTag.name,
  promptCount: apiTag.promptCount || 0,
  color: apiTag.color || '#10B981'
});

export const usePromptStore = () => {
  const { isAuthenticated } = useAuth();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load data from API when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('🔄 Loading data from API...');

        // Load all data in parallel
        const [promptsResponse, categoriesResponse, tagsResponse] = await Promise.all([
          apiClient.getPrompts({ limit: 1000 }),
          apiClient.getCategories(),
          apiClient.getTags()
        ]);

        // Transform and set data
        const transformedPrompts = promptsResponse.prompts.map(transformApiPrompt);
        const transformedCategories = categoriesResponse.categories.map(transformApiCategory);
        const transformedTags = tagsResponse.tags.map(transformApiTag);

        setPrompts(transformedPrompts);
        setCategories(transformedCategories);
        setTags(transformedTags);

        console.log('✅ Data loaded successfully:', {
          prompts: transformedPrompts.length,
          categories: transformedCategories.length,
          tags: transformedTags.length
        });

      } catch (error) {
        console.error('❌ Failed to load data:', error);
        setError('Failed to load data from server');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated]);

  // Helper function to refresh data
  const refreshData = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const [promptsResponse, categoriesResponse, tagsResponse] = await Promise.all([
        apiClient.getPrompts({ limit: 1000 }),
        apiClient.getCategories(),
        apiClient.getTags()
      ]);

      setPrompts(promptsResponse.prompts.map(transformApiPrompt));
      setCategories(categoriesResponse.categories.map(transformApiCategory));
      setTags(tagsResponse.tags.map(transformApiTag));
    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
    }
  }, [isAuthenticated]);

  const getDefaultCategories = (): Category[] => [
    { id: '1', name: 'Content Creation', promptCount: 0, color: '#3B82F6' },
    { id: '2', name: 'Code Generation', promptCount: 0, color: '#10B981' },
    { id: '3', name: 'Analysis', promptCount: 0, color: '#F59E0B' },
    { id: '4', name: 'Creative Writing', promptCount: 0, color: '#8B5CF6' },
    { id: '5', name: 'Business', promptCount: 0, color: '#EF4444' },
    { id: '6', name: 'Research', promptCount: 0, color: '#06B6D4' },
  ];

  const getSamplePrompts = (): Prompt[] => [
    {
      id: '1',
      title: 'Blog Post Generator',
      content: 'Write a comprehensive blog post about [TOPIC]. Include an engaging introduction, 3-5 main sections with detailed explanations, and a compelling conclusion. Target audience: [AUDIENCE]. Tone: [TONE]. Word count: approximately [WORD_COUNT] words.',
      description: 'Generate structured blog posts with customizable parameters',
      category: 'Content Creation',
      tags: ['blog', 'writing', 'content', 'marketing'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      usageCount: 15,
      lastUsed: new Date('2024-01-20')
    },
    {
      id: '2',
      title: 'React Component Builder',
      content: 'Create a React functional component named [COMPONENT_NAME] that:\n- Accepts props: [PROPS_LIST]\n- Uses TypeScript for type safety\n- Includes proper error handling\n- Follows modern React patterns (hooks, etc.)\n- Has responsive design using Tailwind CSS\n- Includes accessibility features\n\nComponent purpose: [PURPOSE]',
      description: 'Generate React components with TypeScript and best practices',
      category: 'Code Generation',
      tags: ['react', 'typescript', 'component', 'frontend'],
      isFavorite: false,
      version: 2,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-18'),
      usageCount: 8,
      lastUsed: new Date('2024-01-19')
    },
    {
      id: '3',
      title: 'Data Analysis Report',
      content: 'Analyze the following dataset and provide insights:\n\n[DATASET_DESCRIPTION]\n\nPlease provide:\n1. Summary statistics and key findings\n2. Data quality assessment\n3. Trend analysis and patterns\n4. Actionable recommendations\n5. Visualizations suggestions\n6. Potential limitations and caveats\n\nFocus on: [ANALYSIS_FOCUS]',
      description: 'Comprehensive data analysis and reporting template',
      category: 'Analysis',
      tags: ['data', 'analysis', 'insights', 'reporting'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12'),
      usageCount: 5,
      lastUsed: new Date('2024-01-17')
    }
  ];

  const updateCategoriesAndTags = useCallback((promptList: Prompt[], categoryList?: Category[]) => {
    try {
      // Update category counts
      const categoryMap = new Map<string, number>();
      const tagMap = new Map<string, number>();

      promptList.forEach(prompt => {
        if (prompt.category) {
          categoryMap.set(prompt.category, (categoryMap.get(prompt.category) || 0) + 1);
        }
        if (Array.isArray(prompt.tags)) {
          prompt.tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
            }
          });
        }
      });

      // Update categories with counts
      const categoriesToUpdate = categoryList || categories;
      const updatedCategories = categoriesToUpdate.map(cat => ({
        ...cat,
        promptCount: categoryMap.get(cat.name) || 0
      }));

      // Create tags from usage
      const newTags: Tag[] = Array.from(tagMap.entries()).map(([name, count]) => ({
        id: name,
        name,
        promptCount: count,
        color: getRandomColor()
      }));

      setCategories(updatedCategories);
      setTags(newTags);

      console.log('📊 Updated categories and tags:', {
        categories: updatedCategories.length,
        tags: newTags.length
      });
    } catch (error) {
      console.error('❌ Error updating categories and tags:', error);
    }
  }, [categories]);

  const getRandomColor = () => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444', '#06B6D4', '#84CC16', '#F97316'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const addPrompt = useCallback((promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    console.log('🔥 ADDPROMPT CALLED - START');
    console.log('📥 Input data:', JSON.stringify(promptData, null, 2));
    console.log('📊 Current prompts array length:', prompts.length);
    console.log('📊 Current prompts:', prompts.map(p => ({ id: p.id, title: p.title })));

    try {
      // Validate required fields
      if (!promptData.title?.trim()) {
        throw new Error('Title is required');
      }
      if (!promptData.content?.trim()) {
        throw new Error('Content is required');
      }
      if (!promptData.category?.trim()) {
        throw new Error('Category is required');
      }

      console.log('✅ Validation passed');

      // Create new prompt with guaranteed unique ID
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substr(2, 9);
      const uniqueId = `${timestamp}-${randomSuffix}`;

      const newPrompt: Prompt = {
        id: uniqueId,
        title: promptData.title.trim(),
        content: promptData.content.trim(),
        description: promptData.description?.trim() || '',
        category: promptData.category.trim(),
        tags: Array.isArray(promptData.tags) ? promptData.tags.filter(tag => tag?.trim()) : [],
        isFavorite: Boolean(promptData.isFavorite),
        version: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        usageCount: 0
      };

      console.log('🆕 New prompt object created:', JSON.stringify(newPrompt, null, 2));

      // FORCE IMMEDIATE STATE UPDATE
      const updatedPrompts = [...prompts, newPrompt];
      console.log('📊 Updated prompts array length:', updatedPrompts.length);
      console.log('📊 Updated prompts:', updatedPrompts.map(p => ({ id: p.id, title: p.title })));

      // Use functional update to ensure we get the latest state
      setPrompts(currentPrompts => {
        console.log('🔄 setPrompts callback - current length:', currentPrompts.length);
        const newArray = [...currentPrompts, newPrompt];
        console.log('🔄 setPrompts callback - new length:', newArray.length);

        // FORCE IMMEDIATE SAVE TO LOCALSTORAGE
        const dataToSave = {
          prompts: newArray,
          categories,
          tags,
          lastUpdated: new Date().toISOString(),
          version: '2.0'
        };

        console.log('💾 FORCE SAVING TO LOCALSTORAGE:', {
          promptsCount: newArray.length,
          newPromptId: newPrompt.id
        });

        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
          localStorage.setItem(BACKUP_KEY, JSON.stringify(dataToSave));
          console.log('✅ FORCE SAVE SUCCESSFUL');
        } catch (saveError) {
          console.error('❌ FORCE SAVE FAILED:', saveError);
        }

        return newArray;
      });

      updateCategoriesAndTags(updatedPrompts);
      setError(null);

      console.log('🔥 ADDPROMPT COMPLETED SUCCESSFULLY');
      return newPrompt;

    } catch (error) {
      console.error('❌ ADDPROMPT FAILED:', error);
      setError(`Failed to add prompt: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }, [prompts, categories, tags, updateCategoriesAndTags]);

  const updatePrompt = (id: string, promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          ...promptData,
          version: prompt.version + 1,
          updatedAt: new Date()
        };
      }
      return prompt;
    });

    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const deletePrompt = (id: string) => {
    const updatedPrompts = prompts.filter(prompt => prompt.id !== id);
    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const searchPrompts = (searchTerm: string): Prompt[] => {
    const term = searchTerm.toLowerCase();
    return prompts.filter(prompt =>
      prompt.title.toLowerCase().includes(term) ||
      prompt.content.toLowerCase().includes(term) ||
      prompt.description?.toLowerCase().includes(term) ||
      prompt.tags.some(tag => tag.toLowerCase().includes(term)) ||
      prompt.category.toLowerCase().includes(term)
    );
  };

  const getPromptsByCategory = (category: string): Prompt[] => {
    return prompts.filter(prompt => prompt.category === category);
  };

  const getPromptsByTags = (tags: string[]): Prompt[] => {
    return prompts.filter(prompt =>
      tags.every(tag => prompt.tags.includes(tag))
    );
  };

  const toggleFavorite = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return { ...prompt, isFavorite: !prompt.isFavorite };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const incrementUsage = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsed: new Date()
        };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const exportData = () => {
    return {
      prompts,
      categories,
      tags,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  };

  const importData = (data: any) => {
    try {
      if (data.prompts) {
        setPrompts(data.prompts);
        updateCategoriesAndTags(data.prompts);
      }
      if (data.categories) {
        setCategories(data.categories);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  };

  return {
    prompts,
    categories,
    tags,
    isLoading,
    error,
    addPrompt,
    updatePrompt,
    deletePrompt,
    searchPrompts,
    getPromptsByCategory,
    getPromptsByTags,
    toggleFavorite,
    incrementUsage,
    exportData,
    importData,
    clearStorage: StorageManager.clear
  };
};
