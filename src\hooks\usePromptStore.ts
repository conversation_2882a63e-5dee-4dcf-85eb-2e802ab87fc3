
import { useState, useEffect, useCallback, useRef } from 'react';
import { Prompt, Category, Tag } from '@/types/prompt';

const STORAGE_KEY = 'ai-prompt-manager-v2';
const BACKUP_KEY = 'ai-prompt-manager-backup';

// Storage utility functions
const StorageManager = {
  save: (data: any): boolean => {
    try {
      const serialized = JSON.stringify(data);
      localStorage.setItem(STORAGE_KEY, serialized);
      // Keep a backup
      localStorage.setItem(BACKUP_KEY, serialized);
      console.log('✅ Data saved successfully:', { promptsCount: data.prompts?.length || 0 });
      return true;
    } catch (error) {
      console.error('❌ Failed to save data:', error);
      return false;
    }
  },

  load: (): any | null => {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (data) {
        const parsed = JSON.parse(data);
        console.log('✅ Data loaded successfully:', { promptsCount: parsed.prompts?.length || 0 });
        return parsed;
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to load data, trying backup:', error);
      try {
        const backup = localStorage.getItem(BACKUP_KEY);
        if (backup) {
          const parsed = JSON.parse(backup);
          console.log('✅ Backup data loaded:', { promptsCount: parsed.prompts?.length || 0 });
          return parsed;
        }
      } catch (backupError) {
        console.error('❌ Backup also failed:', backupError);
      }
      return null;
    }
  },

  clear: (): void => {
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(BACKUP_KEY);
    console.log('🗑️ Storage cleared');
  }
};

export const usePromptStore = () => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use ref to track if we're in the middle of an update
  const isUpdatingRef = useRef(false);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = () => {
      setIsLoading(true);
      setError(null);

      try {
        const savedData = StorageManager.load();

        if (savedData && savedData.prompts && Array.isArray(savedData.prompts)) {
          // Convert date strings back to Date objects
          const promptsWithDates = savedData.prompts.map((prompt: any) => ({
            ...prompt,
            id: prompt.id || Date.now().toString(),
            createdAt: prompt.createdAt ? new Date(prompt.createdAt) : new Date(),
            updatedAt: prompt.updatedAt ? new Date(prompt.updatedAt) : new Date(),
            lastUsed: prompt.lastUsed ? new Date(prompt.lastUsed) : undefined,
            version: prompt.version || 1,
            usageCount: prompt.usageCount || 0,
            isFavorite: Boolean(prompt.isFavorite),
            tags: Array.isArray(prompt.tags) ? prompt.tags : [],
            category: prompt.category || 'Uncategorized'
          }));

          setPrompts(promptsWithDates);
          setCategories(savedData.categories || getDefaultCategories());
          setTags(savedData.tags || []);

          console.log('📊 Loaded existing data:', {
            prompts: promptsWithDates.length,
            categories: (savedData.categories || []).length,
            tags: (savedData.tags || []).length
          });
        } else {
          // Initialize with default data
          const defaultCategories = getDefaultCategories();
          const samplePrompts = getSamplePrompts();

          setCategories(defaultCategories);
          setPrompts(samplePrompts);
          updateCategoriesAndTags(samplePrompts, defaultCategories);

          console.log('🆕 Initialized with sample data:', {
            prompts: samplePrompts.length,
            categories: defaultCategories.length
          });
        }
      } catch (error) {
        console.error('❌ Failed to initialize data:', error);
        setError('Failed to load data. Starting fresh.');

        // Fallback to default data
        const defaultCategories = getDefaultCategories();
        setCategories(defaultCategories);
        setPrompts([]);
        setTags([]);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  // Auto-save with debouncing
  useEffect(() => {
    if (isLoading || isUpdatingRef.current) return;

    const timeoutId = setTimeout(() => {
      const dataToSave = {
        prompts,
        categories,
        tags,
        lastUpdated: new Date().toISOString(),
        version: '2.0'
      };

      const success = StorageManager.save(dataToSave);
      if (!success) {
        setError('Failed to save data to storage');
      }
    }, 500); // Debounce saves by 500ms

    return () => clearTimeout(timeoutId);
  }, [prompts, categories, tags, isLoading]);

  const getDefaultCategories = (): Category[] => [
    { id: '1', name: 'Content Creation', promptCount: 0, color: '#3B82F6' },
    { id: '2', name: 'Code Generation', promptCount: 0, color: '#10B981' },
    { id: '3', name: 'Analysis', promptCount: 0, color: '#F59E0B' },
    { id: '4', name: 'Creative Writing', promptCount: 0, color: '#8B5CF6' },
    { id: '5', name: 'Business', promptCount: 0, color: '#EF4444' },
    { id: '6', name: 'Research', promptCount: 0, color: '#06B6D4' },
  ];

  const getSamplePrompts = (): Prompt[] => [
    {
      id: '1',
      title: 'Blog Post Generator',
      content: 'Write a comprehensive blog post about [TOPIC]. Include an engaging introduction, 3-5 main sections with detailed explanations, and a compelling conclusion. Target audience: [AUDIENCE]. Tone: [TONE]. Word count: approximately [WORD_COUNT] words.',
      description: 'Generate structured blog posts with customizable parameters',
      category: 'Content Creation',
      tags: ['blog', 'writing', 'content', 'marketing'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      usageCount: 15,
      lastUsed: new Date('2024-01-20')
    },
    {
      id: '2',
      title: 'React Component Builder',
      content: 'Create a React functional component named [COMPONENT_NAME] that:\n- Accepts props: [PROPS_LIST]\n- Uses TypeScript for type safety\n- Includes proper error handling\n- Follows modern React patterns (hooks, etc.)\n- Has responsive design using Tailwind CSS\n- Includes accessibility features\n\nComponent purpose: [PURPOSE]',
      description: 'Generate React components with TypeScript and best practices',
      category: 'Code Generation',
      tags: ['react', 'typescript', 'component', 'frontend'],
      isFavorite: false,
      version: 2,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-18'),
      usageCount: 8,
      lastUsed: new Date('2024-01-19')
    },
    {
      id: '3',
      title: 'Data Analysis Report',
      content: 'Analyze the following dataset and provide insights:\n\n[DATASET_DESCRIPTION]\n\nPlease provide:\n1. Summary statistics and key findings\n2. Data quality assessment\n3. Trend analysis and patterns\n4. Actionable recommendations\n5. Visualizations suggestions\n6. Potential limitations and caveats\n\nFocus on: [ANALYSIS_FOCUS]',
      description: 'Comprehensive data analysis and reporting template',
      category: 'Analysis',
      tags: ['data', 'analysis', 'insights', 'reporting'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12'),
      usageCount: 5,
      lastUsed: new Date('2024-01-17')
    }
  ];

  const updateCategoriesAndTags = useCallback((promptList: Prompt[], categoryList?: Category[]) => {
    try {
      // Update category counts
      const categoryMap = new Map<string, number>();
      const tagMap = new Map<string, number>();

      promptList.forEach(prompt => {
        if (prompt.category) {
          categoryMap.set(prompt.category, (categoryMap.get(prompt.category) || 0) + 1);
        }
        if (Array.isArray(prompt.tags)) {
          prompt.tags.forEach(tag => {
            if (tag && typeof tag === 'string') {
              tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
            }
          });
        }
      });

      // Update categories with counts
      const categoriesToUpdate = categoryList || categories;
      const updatedCategories = categoriesToUpdate.map(cat => ({
        ...cat,
        promptCount: categoryMap.get(cat.name) || 0
      }));

      // Create tags from usage
      const newTags: Tag[] = Array.from(tagMap.entries()).map(([name, count]) => ({
        id: name,
        name,
        promptCount: count,
        color: getRandomColor()
      }));

      setCategories(updatedCategories);
      setTags(newTags);

      console.log('📊 Updated categories and tags:', {
        categories: updatedCategories.length,
        tags: newTags.length
      });
    } catch (error) {
      console.error('❌ Error updating categories and tags:', error);
    }
  }, [categories]);

  const getRandomColor = () => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444', '#06B6D4', '#84CC16', '#F97316'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const addPrompt = useCallback((promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    try {
      console.log('🆕 Adding new prompt:', promptData);

      // Validate required fields
      if (!promptData.title?.trim()) {
        throw new Error('Title is required');
      }
      if (!promptData.content?.trim()) {
        throw new Error('Content is required');
      }
      if (!promptData.category?.trim()) {
        throw new Error('Category is required');
      }

      // Set updating flag to prevent auto-save conflicts
      isUpdatingRef.current = true;

      // Create new prompt with guaranteed unique ID
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substr(2, 9);
      const uniqueId = `${timestamp}-${randomSuffix}`;

      const newPrompt: Prompt = {
        id: uniqueId,
        title: promptData.title.trim(),
        content: promptData.content.trim(),
        description: promptData.description?.trim() || '',
        category: promptData.category.trim(),
        tags: Array.isArray(promptData.tags) ? promptData.tags.filter(tag => tag?.trim()) : [],
        isFavorite: Boolean(promptData.isFavorite),
        version: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        usageCount: 0
      };

      console.log('✅ New prompt created:', {
        id: newPrompt.id,
        title: newPrompt.title,
        category: newPrompt.category,
        tags: newPrompt.tags
      });

      // Update state
      const updatedPrompts = [...prompts, newPrompt];

      setPrompts(updatedPrompts);
      updateCategoriesAndTags(updatedPrompts);

      console.log('📊 Prompts updated:', {
        before: prompts.length,
        after: updatedPrompts.length,
        newPromptId: newPrompt.id
      });

      // Clear error state
      setError(null);

      return newPrompt;
    } catch (error) {
      console.error('❌ Failed to add prompt:', error);
      setError(`Failed to add prompt: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    } finally {
      // Reset updating flag after a delay
      setTimeout(() => {
        isUpdatingRef.current = false;
      }, 100);
    }
  }, [prompts, updateCategoriesAndTags]);

  const updatePrompt = (id: string, promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          ...promptData,
          version: prompt.version + 1,
          updatedAt: new Date()
        };
      }
      return prompt;
    });

    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const deletePrompt = (id: string) => {
    const updatedPrompts = prompts.filter(prompt => prompt.id !== id);
    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const searchPrompts = (searchTerm: string): Prompt[] => {
    const term = searchTerm.toLowerCase();
    return prompts.filter(prompt =>
      prompt.title.toLowerCase().includes(term) ||
      prompt.content.toLowerCase().includes(term) ||
      prompt.description?.toLowerCase().includes(term) ||
      prompt.tags.some(tag => tag.toLowerCase().includes(term)) ||
      prompt.category.toLowerCase().includes(term)
    );
  };

  const getPromptsByCategory = (category: string): Prompt[] => {
    return prompts.filter(prompt => prompt.category === category);
  };

  const getPromptsByTags = (tags: string[]): Prompt[] => {
    return prompts.filter(prompt =>
      tags.every(tag => prompt.tags.includes(tag))
    );
  };

  const toggleFavorite = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return { ...prompt, isFavorite: !prompt.isFavorite };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const incrementUsage = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsed: new Date()
        };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const exportData = () => {
    return {
      prompts,
      categories,
      tags,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  };

  const importData = (data: any) => {
    try {
      if (data.prompts) {
        setPrompts(data.prompts);
        updateCategoriesAndTags(data.prompts);
      }
      if (data.categories) {
        setCategories(data.categories);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  };

  return {
    prompts,
    categories,
    tags,
    isLoading,
    error,
    addPrompt,
    updatePrompt,
    deletePrompt,
    searchPrompts,
    getPromptsByCategory,
    getPromptsByTags,
    toggleFavorite,
    incrementUsage,
    exportData,
    importData,
    clearStorage: StorageManager.clear
  };
};
