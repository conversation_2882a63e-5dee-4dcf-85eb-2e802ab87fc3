
import { useState, useEffect } from 'react';
import { Prompt, Category, Tag } from '@/types/prompt';

const STORAGE_KEY = 'ai-prompt-manager';

export const usePromptStore = () => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);

        // Convert date strings back to Date objects
        const promptsWithDates = (parsed.prompts || []).map((prompt: any) => ({
          ...prompt,
          createdAt: new Date(prompt.createdAt),
          updatedAt: new Date(prompt.updatedAt),
          lastUsed: prompt.lastUsed ? new Date(prompt.lastUsed) : undefined
        }));

        setPrompts(promptsWithDates);
        setCategories(parsed.categories || getDefaultCategories());
        setTags(parsed.tags || []);

        console.log('Loaded prompts from localStorage:', promptsWithDates.length);
      } catch (error) {
        console.error('Error loading data from localStorage:', error);
        setCategories(getDefaultCategories());
      }
    } else {
      setCategories(getDefaultCategories());
      // Add sample prompts for demo
      const samplePrompts = getSamplePrompts();
      setPrompts(samplePrompts);
      updateCategoriesAndTags(samplePrompts);
      console.log('Loaded sample prompts:', samplePrompts.length);
    }
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    const dataToSave = {
      prompts,
      categories,
      tags,
      lastUpdated: new Date().toISOString()
    };
    console.log('Saving to localStorage:', {
      promptsCount: prompts.length,
      categoriesCount: categories.length,
      tagsCount: tags.length
    });
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [prompts, categories, tags]);

  const getDefaultCategories = (): Category[] => [
    { id: '1', name: 'Content Creation', promptCount: 0, color: '#3B82F6' },
    { id: '2', name: 'Code Generation', promptCount: 0, color: '#10B981' },
    { id: '3', name: 'Analysis', promptCount: 0, color: '#F59E0B' },
    { id: '4', name: 'Creative Writing', promptCount: 0, color: '#8B5CF6' },
    { id: '5', name: 'Business', promptCount: 0, color: '#EF4444' },
    { id: '6', name: 'Research', promptCount: 0, color: '#06B6D4' },
  ];

  const getSamplePrompts = (): Prompt[] => [
    {
      id: '1',
      title: 'Blog Post Generator',
      content: 'Write a comprehensive blog post about [TOPIC]. Include an engaging introduction, 3-5 main sections with detailed explanations, and a compelling conclusion. Target audience: [AUDIENCE]. Tone: [TONE]. Word count: approximately [WORD_COUNT] words.',
      description: 'Generate structured blog posts with customizable parameters',
      category: 'Content Creation',
      tags: ['blog', 'writing', 'content', 'marketing'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      usageCount: 15,
      lastUsed: new Date('2024-01-20')
    },
    {
      id: '2',
      title: 'React Component Builder',
      content: 'Create a React functional component named [COMPONENT_NAME] that:\n- Accepts props: [PROPS_LIST]\n- Uses TypeScript for type safety\n- Includes proper error handling\n- Follows modern React patterns (hooks, etc.)\n- Has responsive design using Tailwind CSS\n- Includes accessibility features\n\nComponent purpose: [PURPOSE]',
      description: 'Generate React components with TypeScript and best practices',
      category: 'Code Generation',
      tags: ['react', 'typescript', 'component', 'frontend'],
      isFavorite: false,
      version: 2,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-18'),
      usageCount: 8,
      lastUsed: new Date('2024-01-19')
    },
    {
      id: '3',
      title: 'Data Analysis Report',
      content: 'Analyze the following dataset and provide insights:\n\n[DATASET_DESCRIPTION]\n\nPlease provide:\n1. Summary statistics and key findings\n2. Data quality assessment\n3. Trend analysis and patterns\n4. Actionable recommendations\n5. Visualizations suggestions\n6. Potential limitations and caveats\n\nFocus on: [ANALYSIS_FOCUS]',
      description: 'Comprehensive data analysis and reporting template',
      category: 'Analysis',
      tags: ['data', 'analysis', 'insights', 'reporting'],
      isFavorite: true,
      version: 1,
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12'),
      usageCount: 5,
      lastUsed: new Date('2024-01-17')
    }
  ];

  const updateCategoriesAndTags = (promptList: Prompt[]) => {
    // Update category counts
    const categoryMap = new Map<string, number>();
    const tagMap = new Map<string, number>();

    promptList.forEach(prompt => {
      categoryMap.set(prompt.category, (categoryMap.get(prompt.category) || 0) + 1);
      prompt.tags.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
      });
    });

    setCategories(prev => prev.map(cat => ({
      ...cat,
      promptCount: categoryMap.get(cat.name) || 0
    })));

    const newTags: Tag[] = Array.from(tagMap.entries()).map(([name, count]) => ({
      id: name,
      name,
      promptCount: count,
      color: getRandomColor()
    }));

    setTags(newTags);
  };

  const getRandomColor = () => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444', '#06B6D4', '#84CC16', '#F97316'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const addPrompt = (promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    console.log('addPrompt called with:', promptData);
    console.log('Current prompts count:', prompts.length);

    const newPrompt: Prompt = {
      ...promptData,
      id: Date.now().toString(),
      version: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0
    };

    console.log('New prompt created:', newPrompt);

    const updatedPrompts = [...prompts, newPrompt];
    console.log('Updated prompts count:', updatedPrompts.length);

    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const updatePrompt = (id: string, promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          ...promptData,
          version: prompt.version + 1,
          updatedAt: new Date()
        };
      }
      return prompt;
    });

    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const deletePrompt = (id: string) => {
    const updatedPrompts = prompts.filter(prompt => prompt.id !== id);
    setPrompts(updatedPrompts);
    updateCategoriesAndTags(updatedPrompts);
  };

  const searchPrompts = (searchTerm: string): Prompt[] => {
    const term = searchTerm.toLowerCase();
    return prompts.filter(prompt =>
      prompt.title.toLowerCase().includes(term) ||
      prompt.content.toLowerCase().includes(term) ||
      prompt.description?.toLowerCase().includes(term) ||
      prompt.tags.some(tag => tag.toLowerCase().includes(term)) ||
      prompt.category.toLowerCase().includes(term)
    );
  };

  const getPromptsByCategory = (category: string): Prompt[] => {
    return prompts.filter(prompt => prompt.category === category);
  };

  const getPromptsByTags = (tags: string[]): Prompt[] => {
    return prompts.filter(prompt =>
      tags.every(tag => prompt.tags.includes(tag))
    );
  };

  const toggleFavorite = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return { ...prompt, isFavorite: !prompt.isFavorite };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const incrementUsage = (id: string) => {
    const updatedPrompts = prompts.map(prompt => {
      if (prompt.id === id) {
        return {
          ...prompt,
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsed: new Date()
        };
      }
      return prompt;
    });
    setPrompts(updatedPrompts);
  };

  const exportData = () => {
    return {
      prompts,
      categories,
      tags,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  };

  const importData = (data: any) => {
    try {
      if (data.prompts) {
        setPrompts(data.prompts);
        updateCategoriesAndTags(data.prompts);
      }
      if (data.categories) {
        setCategories(data.categories);
      }
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  };

  return {
    prompts,
    categories,
    tags,
    addPrompt,
    updatePrompt,
    deletePrompt,
    searchPrompts,
    getPromptsByCategory,
    getPromptsByTags,
    toggleFavorite,
    incrementUsage,
    exportData,
    importData
  };
};
