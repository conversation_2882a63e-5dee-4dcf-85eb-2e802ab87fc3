const API_BASE_URL = 'http://localhost:3001/api';

// Types for API responses
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: string;
  createdAt: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  message: string;
}

export interface RegisterResponse {
  token: string;
  user: User;
  message: string;
}

// API Client class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  // Set authentication token
  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  // Get authentication token
  getToken(): string | null {
    return this.token || localStorage.getItem('auth_token');
  }

  // Make HTTP request
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      console.log(`🌐 API Request: ${options.method || 'GET'} ${url}`);
      
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        console.error(`❌ API Error: ${response.status}`, data);
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      console.log(`✅ API Success: ${options.method || 'GET'} ${url}`);
      return data;
    } catch (error) {
      console.error(`❌ API Request failed: ${url}`, error);
      throw error;
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Authentication methods
  async login(username: string, password: string): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>('/auth/login', {
      username,
      password,
    });
    
    if (response.token) {
      this.setToken(response.token);
    }
    
    return response;
  }

  async register(userData: {
    username: string;
    email: string;
    password: string;
    fullName?: string;
  }): Promise<RegisterResponse> {
    const response = await this.post<RegisterResponse>('/auth/register', userData);
    
    if (response.token) {
      this.setToken(response.token);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    this.setToken(null);
  }

  async getProfile(): Promise<{ user: User }> {
    return this.get<{ user: User }>('/auth/profile');
  }

  async verifyToken(): Promise<{ valid: boolean; user: User }> {
    return this.get<{ valid: boolean; user: User }>('/auth/verify');
  }

  // Prompt methods
  async getPrompts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    tags?: string;
    own_only?: boolean;
  }): Promise<{
    prompts: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    const queryParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/prompts${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.get(endpoint);
  }

  async getPrompt(id: string): Promise<{ prompt: any }> {
    return this.get(`/prompts/${id}`);
  }

  async createPrompt(promptData: {
    title: string;
    content: string;
    description?: string;
    category: string;
    tags?: string[];
    isFavorite?: boolean;
    isPublic?: boolean;
  }): Promise<{ prompt: any; message: string }> {
    return this.post('/prompts', promptData);
  }

  async updatePrompt(id: string, promptData: Partial<{
    title: string;
    content: string;
    description: string;
    category: string;
    tags: string[];
    isFavorite: boolean;
    isPublic: boolean;
  }>): Promise<{ prompt: any; message: string }> {
    return this.put(`/prompts/${id}`, promptData);
  }

  async deletePrompt(id: string): Promise<{ message: string }> {
    return this.delete(`/prompts/${id}`);
  }

  async incrementPromptUsage(id: string): Promise<{ message: string }> {
    return this.post(`/prompts/${id}/use`);
  }

  // Category methods
  async getCategories(): Promise<{ categories: any[] }> {
    return this.get('/categories');
  }

  // Tag methods
  async getTags(): Promise<{ tags: any[] }> {
    return this.get('/tags');
  }

  async getPopularTags(limit?: number): Promise<{ tags: any[] }> {
    const endpoint = `/tags/popular${limit ? `?limit=${limit}` : ''}`;
    return this.get(endpoint);
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export default
export default apiClient;
