
import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Download, Upload, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Sidebar } from '@/components/Sidebar';
import { PromptCard } from '@/components/PromptCard';
import { PromptEditor } from '@/components/PromptEditor';
import { ImportExportDialog } from '@/components/ImportExportDialog';
import { FilterDialog } from '@/components/FilterDialog';
import { usePromptStore } from '@/hooks/usePromptStore';
import { Prompt } from '@/types/prompt';

const Index = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [isImportExportOpen, setIsImportExportOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const {
    prompts,
    categories,
    tags,
    isLoading,
    error,
    addPrompt,
    updatePrompt,
    deletePrompt,
    searchPrompts,
    getPromptsByCategory,
    getPromptsByTags,
    clearStorage
  } = usePromptStore();

  const [filteredPrompts, setFilteredPrompts] = useState<Prompt[]>(prompts);

  useEffect(() => {
    console.log('🔍 FILTERING PROMPTS - START');
    console.log('📊 Total prompts in store:', prompts.length);
    console.log('📊 All prompts:', prompts.map(p => ({ id: p.id, title: p.title, category: p.category })));
    console.log('🔍 Search term:', searchTerm);
    console.log('🏷️ Selected category:', selectedCategory);
    console.log('🏷️ Selected tags:', selectedTags);

    let results = [...prompts]; // Create a copy
    console.log('📊 Starting with prompts:', results.length);

    if (searchTerm) {
      results = searchPrompts(searchTerm);
      console.log('📊 After search filter:', results.length);
      console.log('📊 Search results:', results.map(p => ({ id: p.id, title: p.title })));
    }

    if (selectedCategory !== 'all') {
      const beforeFilter = results.length;
      results = results.filter(prompt => prompt.category === selectedCategory);
      console.log(`📊 Category filter: ${beforeFilter} → ${results.length} (looking for "${selectedCategory}")`);
      console.log('📊 Category filter results:', results.map(p => ({ id: p.id, title: p.title, category: p.category })));
    }

    if (selectedTags.length > 0) {
      const beforeFilter = results.length;
      results = results.filter(prompt =>
        selectedTags.every(tag => prompt.tags.includes(tag))
      );
      console.log(`📊 Tags filter: ${beforeFilter} → ${results.length} (looking for tags: ${selectedTags.join(', ')})`);
      console.log('📊 Tags filter results:', results.map(p => ({ id: p.id, title: p.title, tags: p.tags })));
    }

    console.log('🔍 FINAL FILTERED RESULTS:', results.length);
    console.log('📊 Final results:', results.map(p => ({ id: p.id, title: p.title })));
    setFilteredPrompts(results);
  }, [searchTerm, selectedCategory, selectedTags, prompts, searchPrompts]);

  const handleCreatePrompt = () => {
    setEditingPrompt(null);
    setIsEditorOpen(true);
  };

  const handleEditPrompt = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    setIsEditorOpen(true);
  };

  const handleSavePrompt = async (promptData: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>) => {
    try {
      console.log('💾 Saving prompt:', promptData);
      console.log('📊 Current prompts before save:', prompts.length);

      if (editingPrompt) {
        updatePrompt(editingPrompt.id, promptData);
        console.log('✅ Updated existing prompt');
      } else {
        const newPrompt = addPrompt(promptData);
        console.log('✅ Added new prompt:', newPrompt?.id);
      }

      // Close editor after successful save
      setIsEditorOpen(false);
      setEditingPrompt(null);

      // Log success after state updates
      setTimeout(() => {
        console.log('📊 Prompts after save:', prompts.length);
      }, 200);

    } catch (error) {
      console.error('❌ Failed to save prompt:', error);
      alert(`Failed to save prompt: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleDeletePrompt = (id: string) => {
    deletePrompt(id);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your prompts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar
        categories={categories}
        tags={tags}
        selectedCategory={selectedCategory}
        selectedTags={selectedTags}
        onCategorySelect={setSelectedCategory}
        onTagSelect={setSelectedTags}
      />

      <div className="flex-1 p-6">
        {/* Error Banner */}
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="text-red-600 mr-3">⚠️</div>
                <div>
                  <h4 className="text-red-800 font-medium">Error</h4>
                  <p className="text-red-700 text-sm">{error}</p>
                </div>
              </div>
              <button
                onClick={() => clearStorage()}
                className="text-red-600 hover:text-red-800 text-sm underline"
              >
                Clear Storage & Restart
              </button>
            </div>
          </div>
        )}
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">AI Prompt Manager</h1>
              <p className="text-gray-600 mt-2">Organize, manage, and optimize your AI prompts efficiently</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setIsImportExportOpen(true)}
                className="flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                Import/Export
              </Button>
              <Button
                onClick={handleCreatePrompt}
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                New Prompt
              </Button>
              <Button
                onClick={() => {
                  console.log('🧪 TEST BUTTON CLICKED');
                  const testPrompt = {
                    title: `Test Prompt ${Date.now()}`,
                    content: 'This is a test prompt content',
                    description: 'Test description',
                    category: 'Content Creation',
                    tags: ['test'],
                    isFavorite: false
                  };
                  console.log('🧪 Calling addPrompt with:', testPrompt);
                  try {
                    addPrompt(testPrompt);
                    console.log('🧪 addPrompt call completed');
                  } catch (error) {
                    console.error('🧪 addPrompt failed:', error);
                  }
                }}
                variant="outline"
                className="bg-red-100 hover:bg-red-200 flex items-center gap-2"
              >
                🧪 Test Add
              </Button>
              {/* Debug Info */}
              <div className="text-xs text-gray-500 flex items-center gap-2 px-2">
                <span>Debug: {prompts.length} prompts</span>
                <span>•</span>
                <span>{filteredPrompts.length} filtered</span>
                {error && <span className="text-red-500">• Error!</span>}
                <button
                  onClick={() => {
                    console.log('🔍 LOCALSTORAGE INSPECTION:');
                    const stored = localStorage.getItem('ai-prompt-manager-v2');
                    const backup = localStorage.getItem('ai-prompt-manager-backup');
                    console.log('📦 Main storage:', stored ? JSON.parse(stored) : 'null');
                    console.log('📦 Backup storage:', backup ? JSON.parse(backup) : 'null');
                    console.log('📊 Current prompts state:', prompts);
                    console.log('📊 Current filtered prompts:', filteredPrompts);
                  }}
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Inspect Storage
                </button>
              </div>
            </div>
          </div>

          {/* Search and Filter Bar */}
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search prompts by title, content, or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setIsFilterOpen(true)}
              className="flex items-center gap-2"
            >
              <Filter className="w-4 h-4" />
              Filters
            </Button>
          </div>

          {/* Active Filters Display */}
          {(selectedCategory !== 'all' || selectedTags.length > 0) && (
            <div className="mt-4 flex flex-wrap gap-2">
              {selectedCategory !== 'all' && (
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                  Category: {selectedCategory}
                </span>
              )}
              {selectedTags.map(tag => (
                <span key={tag} className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500">Total Prompts</h3>
            <p className="text-2xl font-bold text-gray-900 mt-2">{prompts.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500">Categories</h3>
            <p className="text-2xl font-bold text-gray-900 mt-2">{categories.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500">Tags</h3>
            <p className="text-2xl font-bold text-gray-900 mt-2">{tags.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="text-sm font-medium text-gray-500">Filtered Results</h3>
            <p className="text-2xl font-bold text-gray-900 mt-2">{filteredPrompts.length}</p>
          </div>
        </div>

        {/* Prompts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredPrompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onEdit={handleEditPrompt}
              onDelete={handleDeletePrompt}
            />
          ))}
        </div>

        {filteredPrompts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No prompts found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || selectedCategory !== 'all' || selectedTags.length > 0
                ? 'Try adjusting your search criteria or filters'
                : 'Get started by creating your first AI prompt'}
            </p>
            <Button onClick={handleCreatePrompt} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              Create First Prompt
            </Button>
          </div>
        )}
      </div>

      {/* Dialogs */}
      {isEditorOpen && (
        <PromptEditor
          prompt={editingPrompt}
          categories={categories}
          tags={tags}
          onSave={handleSavePrompt}
          onClose={() => {
            setIsEditorOpen(false);
            setEditingPrompt(null);
          }}
        />
      )}

      {isImportExportOpen && (
        <ImportExportDialog
          prompts={prompts}
          onClose={() => setIsImportExportOpen(false)}
        />
      )}

      {isFilterOpen && (
        <FilterDialog
          categories={categories}
          tags={tags}
          selectedCategory={selectedCategory}
          selectedTags={selectedTags}
          onCategorySelect={setSelectedCategory}
          onTagSelect={setSelectedTags}
          onClose={() => setIsFilterOpen(false)}
        />
      )}
    </div>
  );
};

export default Index;
