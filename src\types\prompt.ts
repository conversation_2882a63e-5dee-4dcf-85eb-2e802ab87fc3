
export interface Prompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  category: string;
  tags: string[];
  isFavorite: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
  versions?: PromptVersion[];
  usageCount?: number;
  lastUsed?: Date;
}

export interface PromptVersion {
  id: string;
  version: number;
  content: string;
  title: string;
  description?: string;
  createdAt: Date;
  changeLog?: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  promptCount: number;
}

export interface Tag {
  id: string;
  name: string;
  color?: string;
  promptCount: number;
}

export interface PromptStats {
  totalPrompts: number;
  totalCategories: number;
  totalTags: number;
  recentlyUsed: Prompt[];
  topCategories: Category[];
  topTags: Tag[];
}
